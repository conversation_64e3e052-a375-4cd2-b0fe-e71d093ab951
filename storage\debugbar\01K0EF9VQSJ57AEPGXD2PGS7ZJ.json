{"__meta": {"id": "01K0EF9VQSJ57AEPGXD2PGS7ZJ", "datetime": "2025-07-18 15:54:30", "utime": **********.778796, "method": "GET", "uri": "/password/sent", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.08306, "end": **********.778825, "duration": 0.6957650184631348, "duration_str": "696ms", "measures": [{"label": "Booting", "start": **********.08306, "relative_start": 0, "end": **********.693782, "relative_end": **********.693782, "duration": 0.****************, "duration_str": "611ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.693804, "relative_start": 0.****************, "end": **********.778827, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "85.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.721855, "relative_start": 0.****************, "end": **********.729095, "relative_end": **********.729095, "duration": 0.0072400569915771484, "duration_str": "7.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.754011, "relative_start": 0.****************, "end": **********.774386, "relative_end": **********.774386, "duration": 0.*****************, "duration_str": "20.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "1x auth.passwords.sent", "param_count": null, "params": [], "start": **********.7608, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/auth/passwords/sent.blade.phpauth.passwords.sent", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fauth%2Fpasswords%2Fsent.blade.php&line=1", "ajax": false, "filename": "sent.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.passwords.sent"}, {"name": "1x sweetalert::alert", "param_count": null, "params": [], "start": **********.773044, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "sweetalert::alert"}]}, "route": {"uri": "GET password/sent", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\ForgotPasswordController@showLinkSentPage<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAuth%2FForgotPasswordController.php&line=96\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "password.sent", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAuth%2FForgotPasswordController.php&line=96\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/ForgotPasswordController.php:96-103</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9TgaNO0mLL7UYedMfgdslnahTQpcha4NRzUaRlc9", "_flash": "array:2 [\n  \"old\" => array:2 [\n    0 => \"official_email\"\n    1 => \"alias_name\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/password/reset\"\n]", "official_email": "<EMAIL>", "alias_name": "<PERSON>", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01K0EF9V19TDNBGAASM72D0XBW\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/password/sent", "action_name": "password.sent", "controller_action": "App\\Http\\Controllers\\Auth\\ForgotPasswordController@showLinkSentPage", "uri": "GET password/sent", "controller": "App\\Http\\Controllers\\Auth\\ForgotPasswordController@showLinkSentPage<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAuth%2FForgotPasswordController.php&line=96\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAuth%2FForgotPasswordController.php&line=96\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/ForgotPasswordController.php:96-103</a>", "middleware": "web", "duration": "696ms", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1351121191 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1351121191\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1817468387 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1817468387\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-420810693 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">https://blueorange.test/password/reset</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1072 characters\">laravel_session=eyJpdiI6ImNvQmlPQVRUMFRyZ3FTd21MRm9ib0E9PSIsInZhbHVlIjoiRGpIWCs4QW44Qkgyc2JuL25qRUdCRVgvaTN5cWx0U3YramV2ZU5kdXVRUCtmTUNkazFGZFdDNzZERXZjRExnYVJ6VlgveWRoUDB0YWh1cTVsTHpJenV1NTBwajNDYWVrV09MVkhHdEVjck1UWmk0NEhVZHFJbG5RN1prTFY2SEkiLCJtYWMiOiIzYzE4ZTgxMzZhZjNlMTA0YmY1MjEwYTljZDNmNGU4ZjYyOWE0YjFkNWJhOWFjNWMwOWU3ZTI4MTc4OWE3MDhlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InZUeW13RXByT0VyQmpoUVVKYWVCdVE9PSIsInZhbHVlIjoibUtPTnNZTVZId1RNZ0JTNHQvajRJbDlpNWVQTmVRUVRsYVU1b0hEckNRNkJrN2NqT1JRYlVqay9Sa2VXWS9BbEthdjAraE9lejc3aXNzcHR6dnV6WVFMdUU0VEIyaXZTU0t2OU1zdWtLdUY5SzQ0WXpqNyt5MkxXSDNtMXZGdlAiLCJtYWMiOiI4YmI5NWEyNjVkYmFjNTZlNTBjMTgxNDFmMzIyMWU3NWFiYmI3ODhiMWM2MmFlMTBhZTBhNjJkZjhjNGQ0MTVhIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6InNaWWJiVkQvbjVDQWVlRURCWnpSdlE9PSIsInZhbHVlIjoiMkQxM3hyTDNqSjEvd3l2dnlaNUkxMVFqUFB2L1lUenVCdjVOalUzSklwOGZmQkNBVUpTVFhydW16OGVkOTNUa0pjMTc4aUkzdFVOaVljTElKUWtVZGs2cHhjSkNXT3Z3QjIwY25QREk4SzdJRy84cFRiaUMySGJWdTNZNWJmQWIiLCJtYWMiOiI4N2Y1YzAwZDNiOWI4YTJiYzMyNDQ0Y2M0ZGMyMjdiNDcwOGFmMzE1NjdiOTA1MzczNDI1YTE0NzFhOTliZjI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420810693\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-767938659 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iKklXT5rhoiaJF6thcK0rADvZqtOSIW37uJdoTiC</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9TgaNO0mLL7UYedMfgdslnahTQpcha4NRzUaRlc9</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oZFiw9LBZUee8UfUeLbJuVDiHzMn1WwK1T8bRcpQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767938659\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-626291281 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 09:54:30 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-626291281\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-224629130 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9TgaNO0mLL7UYedMfgdslnahTQpcha4NRzUaRlc9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">official_email</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">alias_name</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">https://blueorange.test/password/reset</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>official_email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>alias_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Ross</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K0EF9V19TDNBGAASM72D0XBW</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224629130\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/password/sent", "action_name": "password.sent", "controller_action": "App\\Http\\Controllers\\Auth\\ForgotPasswordController@showLinkSentPage"}, "badge": null}}