{"__meta": {"id": "01K0EFNHPE1D70XRQNP695540X", "datetime": "2025-07-18 16:00:53", "utime": **********.713071, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.377771, "end": **********.713104, "duration": 2.3353331089019775, "duration_str": "2.34s", "measures": [{"label": "Booting", "start": **********.377771, "relative_start": 0, "end": **********.961038, "relative_end": **********.961038, "duration": 0.****************, "duration_str": "583ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.961056, "relative_start": 0.****************, "end": **********.713108, "relative_end": 4.0531158447265625e-06, "duration": 1.****************, "duration_str": "1.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.983654, "relative_start": 0.****************, "end": **********.989407, "relative_end": **********.989407, "duration": 0.005753040313720703, "duration_str": "5.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.703257, "relative_start": 2.***************, "end": **********.704394, "relative_end": **********.704394, "duration": 0.0011370182037353516, "duration_str": "1.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\LoginController@login<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FAuthenticatesUsers.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FAuthenticatesUsers.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/ui/auth-backend/AuthenticatesUsers.php:32-60</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.018529999999999998, "accumulated_duration_str": "18.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' and `status` = 'Active' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>", "Active"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/LoginController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 87}, {"index": 19, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.088595, "duration": 0.00797, "duration_str": "7.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 43.011}, {"sql": "insert into `login_histories` (`user_id`, `login_time`, `login_ip`, `user_agent`, `updated_at`, `created_at`) values (1, '2025-07-18 16:00:53', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********', '2025-07-18 16:00:53', '2025-07-18 16:00:53')", "type": "query", "params": [], "bindings": [1, "2025-07-18 16:00:53", "**************", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********", "2025-07-18 16:00:53", "2025-07-18 16:00:53"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Listeners/LogSuccessfulLogin.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Listeners\\LogSuccessfulLogin.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 745}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 509}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 387}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Auth/LoginController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 87}], "start": **********.673181, "duration": 0.01056, "duration_str": "10.56ms", "memory": 0, "memory_str": null, "filename": "LogSuccessfulLogin.php:27", "source": {"index": 21, "namespace": null, "name": "app/Listeners/LogSuccessfulLogin.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Listeners\\LogSuccessfulLogin.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FListeners%2FLogSuccessfulLogin.php&line=27", "ajax": false, "filename": "LogSuccessfulLogin.php", "line": "27"}, "connection": "blueorange", "explain": null, "start_percent": 43.011, "width_percent": 56.989}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"data": {"status": "302 Found", "full_url": "https://blueorange.test/login", "action_name": null, "controller_action": "App\\Http\\Controllers\\Auth\\LoginController@login", "uri": "POST login", "controller": "App\\Http\\Controllers\\Auth\\LoginController@login<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FAuthenticatesUsers.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FAuthenticatesUsers.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/ui/auth-backend/AuthenticatesUsers.php:32-60</a>", "middleware": "web", "duration": "2.34s", "peak_memory": "30MB", "response": "Redirect to https://blueorange.test/dashboard", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1839770245 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1839770245\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-47912922 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9TgaNO0mLL7UYedMfgdslnahTQpcha4NRzUaRlc9</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"18 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47912922\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1207128220 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">99</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">https://blueorange.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1072 characters\">laravel_session=eyJpdiI6ImNvQmlPQVRUMFRyZ3FTd21MRm9ib0E9PSIsInZhbHVlIjoiRGpIWCs4QW44Qkgyc2JuL25qRUdCRVgvaTN5cWx0U3YramV2ZU5kdXVRUCtmTUNkazFGZFdDNzZERXZjRExnYVJ6VlgveWRoUDB0YWh1cTVsTHpJenV1NTBwajNDYWVrV09MVkhHdEVjck1UWmk0NEhVZHFJbG5RN1prTFY2SEkiLCJtYWMiOiIzYzE4ZTgxMzZhZjNlMTA0YmY1MjEwYTljZDNmNGU4ZjYyOWE0YjFkNWJhOWFjNWMwOWU3ZTI4MTc4OWE3MDhlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InZub0c4OENYRkFhRDFINDVBVU1mTlE9PSIsInZhbHVlIjoiVzlaODY3Z2s1Zm1KVnZETGtEQW56b3hZYXFab3Ztb3ozZFdNa1kwbWhUT2VaR0MwR1IzLytmSXZyajluZWVDeG5CbjZjNGJZUytvakgxUTd1R1J0MWp5aXdadUtiZmJWck5KdHVKMDJJdFRSWXFXQXdHN2hIaUEvOWd3TGEyTFgiLCJtYWMiOiJkODVhYjY2NWFiNWMwYjM3MzEwMjFkYzhhMTA2M2QxZDJhOWRhZDUxZTFjM2I5YTQ0NmY2YzhjN2FhNTlmYmY2IiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6ImFJS2J2L1ErcSs4S2hSb1l1WE5vcFE9PSIsInZhbHVlIjoiMWZGMWd4b1A4OGpaQXVzUmxieFNuNXFOMUV2VXZDSGJwM3IrdHJpWVdmYlp2ckdkbk0yY2hXMlI1eXNvTkhud3o4OXkrZDlrNU1tOHBtcnZRMUo5dVUvdEEwc1RubjUrenFYeER0MmQrVW5WZkZLWnQ5SWhLZ1RJOC9UL3hGWHEiLCJtYWMiOiJkOWQ1OGY0ZjM1NDViNGJiNjNkNmNhODU3ODdjZGYxNGU5NjdhYWM1OWNkNzVlYjllOGI5ODg0MTgzNTQwNmNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207128220\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-691681668 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iKklXT5rhoiaJF6thcK0rADvZqtOSIW37uJdoTiC</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9TgaNO0mLL7UYedMfgdslnahTQpcha4NRzUaRlc9</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oZFiw9LBZUee8UfUeLbJuVDiHzMn1WwK1T8bRcpQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691681668\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1681195545 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 10:00:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">https://blueorange.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681195545\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-445916000 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">https://blueorange.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445916000\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://blueorange.test/login", "controller_action": "App\\Http\\Controllers\\Auth\\LoginController@login"}, "badge": "302 Found"}}