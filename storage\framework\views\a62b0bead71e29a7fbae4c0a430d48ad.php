<div class="card mb-4">
    <div class="card-header header-elements pt-3 pb-3">
        <h5 class="mb-0"><?php echo e(__('Sub Tasks')); ?></h5>
        <div class="card-header-elements ms-auto">
            <button id="toggleView" class="btn btn-xs btn-outline-dark" title="Switch View" style="padding: 3px;">
                <span class="tf-icon ti ti-layout-2"></span>
            </button>
        </div>
    </div>

    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="demo-inline-spacing mt-1">
                    <div id="taskContainer" class="list-group list-view">
                        <?php $__empty_1 = true; $__currentLoopData = $task->sub_tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <a href="<?php echo e(route('administration.task.show', ['task' => $task, 'taskid' => $task->taskid])); ?>" class="list-group-item d-flex justify-content-between btn-outline-<?php echo e(getColor($task->status)); ?> bg-label-<?php echo e(getColor($task->status)); ?> mb-3" style="border-radius: 5px;">
                                <div class="li-wrapper d-flex justify-content-start align-items-center" title="<?php echo e($task->title); ?>">
                                    <div class="list-content">
                                        <h6 class="mb-1 text-dark text-bold"><?php echo e(show_content($task->title, 30)); ?></h6>
                                        <small class="text-muted">Task ID: <b><?php echo e($task->taskid); ?></b></small>
                                    </div>
                                </div>
                                <div class="li-wrapper d-flex justify-content-start align-items-center">
                                    <div class="list-content">
                                        <?php if(!is_null($task->deadline)): ?>
                                            <b class="text-dark" title="Task Deadline"><?php echo e(show_date($task->deadline)); ?></b>
                                        <?php else: ?>
                                            <span class="badge bg-success" title="Task Deadline">Ongoing Task</span>
                                        <?php endif; ?>
                                        <br>
                                        <small class="text-dark">Created: <span class="text-muted"><?php echo e(show_date($task->created_at)); ?></span></small>
                                    </div>
                                </div>
                                <div class="li-wrapper d-flex justify-content-start align-items-center li-task-status-priority">
                                    <div class="list-content text-center">
                                        <small class="badge bg-<?php echo e(getColor($task->status)); ?> mb-1 task-status" title="Task Status"><?php echo e($task->status); ?></small>
                                        <br>
                                        <small class="badge bg-<?php echo e(getColor($task->priority)); ?> task-priority" title="Task Priority"><?php echo e($task->priority); ?></small>
                                    </div>
                                </div>
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <h4 class="text-center text-muted mt-3"><?php echo e(__('No Tasks Available')); ?></h4>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/task/includes/sub_tasks.blade.php ENDPATH**/ ?>