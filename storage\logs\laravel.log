[2025-07-18 15:57:07] local.ERROR: Uncaught Symfony\Component\Finder\Exception\DirectoryNotFoundException: The "E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views\emails\auth" directory does not exist. in E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\vendor\symfony\finder\Finder.php:649
Stack trace:
#0 E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(614): Symfony\Component\Finder\Finder->in('E:\\NIGEL\\Larave...')
#1 Command line code(1): Illuminate\Filesystem\Filesystem->directories('E:\\NIGEL\\Larave...')
#2 Command line code(1): getViewsFromDirectory(Array, Object(Illuminate\Filesystem\Filesystem), 'emails')
#3 Command line code(1): getViewsFromDirectory(Array, Object(Illuminate\Filesystem\Filesystem))
#4 Command line code(1): getViews('E:\\NIGEL\\Larave...', Object(Illuminate\Filesystem\Filesystem))
#5 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException: The \"E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views\\emails\\auth\" directory does not exist. in E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\symfony\\finder\\Finder.php:649
Stack trace:
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(614): Symfony\\Component\\Finder\\Finder->in('E:\\\\NIGEL\\\\Larave...')
#1 Command line code(1): Illuminate\\Filesystem\\Filesystem->directories('E:\\\\NIGEL\\\\Larave...')
#2 Command line code(1): getViewsFromDirectory(Array, Object(Illuminate\\Filesystem\\Filesystem), 'emails')
#3 Command line code(1): getViewsFromDirectory(Array, Object(Illuminate\\Filesystem\\Filesystem))
#4 Command line code(1): getViews('E:\\\\NIGEL\\\\Larave...', Object(Illuminate\\Filesystem\\Filesystem))
#5 {main}
  thrown at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\symfony\\finder\\Finder.php:649)
[stacktrace]
#0 {main}
"} 
