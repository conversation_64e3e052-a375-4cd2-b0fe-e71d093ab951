{"__meta": {"id": "01K0EHDJ9K4NSE3A6XGD6DBXHP", "datetime": "2025-07-18 16:31:29", "utime": **********.333489, "method": "GET", "uri": "/task/show/ny9qVRQEBxdzBJK6/BOT20250717215526", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.09115, "end": **********.333525, "duration": 2.242374897003174, "duration_str": "2.24s", "measures": [{"label": "Booting", "start": **********.09115, "relative_start": 0, "end": **********.947262, "relative_end": **********.947262, "duration": 0.***************, "duration_str": "856ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.947278, "relative_start": 0.****************, "end": **********.333529, "relative_end": 4.0531158447265625e-06, "duration": 1.****************, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.968073, "relative_start": 0.****************, "end": **********.978278, "relative_end": **********.978278, "duration": 0.*****************, "duration_str": "10.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.326352, "relative_start": 1.****************, "end": **********.327872, "relative_end": **********.327872, "duration": 1.****************, "duration_str": "1s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "33MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 36, "nb_templates": 36, "templates": [{"name": "1x administration.task.show", "param_count": null, "params": [], "start": **********.332942, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/show.blade.phpadministration.task.show", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Ftask%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.task.show"}, {"name": "1x administration.task.includes.about_task", "param_count": null, "params": [], "start": **********.39835, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/includes/about_task.blade.phpadministration.task.includes.about_task", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Ftask%2Fincludes%2Fabout_task.blade.php&line=1", "ajax": false, "filename": "about_task.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.task.includes.about_task"}, {"name": "1x administration.task.includes.task_assignees", "param_count": null, "params": [], "start": **********.657504, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/includes/task_assignees.blade.phpadministration.task.includes.task_assignees", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Ftask%2Fincludes%2Ftask_assignees.blade.php&line=1", "ajax": false, "filename": "task_assignees.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.task.includes.task_assignees"}, {"name": "1x administration.task.modals.add_assignees", "param_count": null, "params": [], "start": **********.665879, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.phpadministration.task.modals.add_assignees", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Ftask%2Fmodals%2Fadd_assignees.blade.php&line=1", "ajax": false, "filename": "add_assignees.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.task.modals.add_assignees"}, {"name": "1x administration.task.modals.remove_assignee", "param_count": null, "params": [], "start": **********.043282, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/remove_assignee.blade.phpadministration.task.modals.remove_assignee", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Ftask%2Fmodals%2Fremove_assignee.blade.php&line=1", "ajax": false, "filename": "remove_assignee.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.task.modals.remove_assignee"}, {"name": "1x administration.task.includes.task_details", "param_count": null, "params": [], "start": **********.045187, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/includes/task_details.blade.phpadministration.task.includes.task_details", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Ftask%2Fincludes%2Ftask_details.blade.php&line=1", "ajax": false, "filename": "task_details.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.task.includes.task_details"}, {"name": "1x administration.task.includes.task_files", "param_count": null, "params": [], "start": **********.110387, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/includes/task_files.blade.phpadministration.task.includes.task_files", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Ftask%2Fincludes%2Ftask_files.blade.php&line=1", "ajax": false, "filename": "task_files.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.task.includes.task_files"}, {"name": "1x administration.task.modals.add_task_files", "param_count": null, "params": [], "start": **********.118383, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_task_files.blade.phpadministration.task.modals.add_task_files", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Ftask%2Fmodals%2Fadd_task_files.blade.php&line=1", "ajax": false, "filename": "add_task_files.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.task.modals.add_task_files"}, {"name": "1x administration.task.includes.sub_tasks", "param_count": null, "params": [], "start": **********.120257, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/includes/sub_tasks.blade.phpadministration.task.includes.sub_tasks", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Ftask%2Fincludes%2Fsub_tasks.blade.php&line=1", "ajax": false, "filename": "sub_tasks.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.task.includes.sub_tasks"}, {"name": "1x administration.task.includes.task_comments", "param_count": null, "params": [], "start": **********.121733, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/includes/task_comments.blade.phpadministration.task.includes.task_comments", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Ftask%2Fincludes%2Ftask_comments.blade.php&line=1", "ajax": false, "filename": "task_comments.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.task.includes.task_comments"}, {"name": "1x administration.task.modals.task_status", "param_count": null, "params": [], "start": **********.124207, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/task_status.blade.phpadministration.task.modals.task_status", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Ftask%2Fmodals%2Ftask_status.blade.php&line=1", "ajax": false, "filename": "task_status.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.task.modals.task_status"}, {"name": "1x layouts.administration.app", "param_count": null, "params": [], "start": **********.125782, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/app.blade.phplayouts.administration.app", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.app"}, {"name": "1x layouts.administration.partials.metas", "param_count": null, "params": [], "start": **********.126919, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/metas.blade.phplayouts.administration.partials.metas", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmetas.blade.php&line=1", "ajax": false, "filename": "metas.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.metas"}, {"name": "1x layouts.administration.partials.stylesheet", "param_count": null, "params": [], "start": **********.127749, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/stylesheet.blade.phplayouts.administration.partials.stylesheet", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fstylesheet.blade.php&line=1", "ajax": false, "filename": "stylesheet.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.stylesheet"}, {"name": "1x layouts.administration.partials.sidenav", "param_count": null, "params": [], "start": **********.128867, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/sidenav.blade.phplayouts.administration.partials.sidenav", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fsidenav.blade.php&line=1", "ajax": false, "filename": "sidenav.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.sidenav"}, {"name": "1x layouts.administration.partials.menus.dashboard", "param_count": null, "params": [], "start": **********.134122, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/dashboard.blade.phplayouts.administration.partials.menus.dashboard", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.dashboard"}, {"name": "1x layouts.administration.partials.menus.chatting", "param_count": null, "params": [], "start": **********.135337, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/chatting.blade.phplayouts.administration.partials.menus.chatting", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fchatting.blade.php&line=1", "ajax": false, "filename": "chatting.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.chatting"}, {"name": "1x layouts.administration.partials.menus.vault", "param_count": null, "params": [], "start": **********.140559, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/vault.blade.phplayouts.administration.partials.menus.vault", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fvault.blade.php&line=1", "ajax": false, "filename": "vault.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.vault"}, {"name": "1x layouts.administration.partials.menus.attendance", "param_count": null, "params": [], "start": **********.144635, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/attendance.blade.phplayouts.administration.partials.menus.attendance", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fattendance.blade.php&line=1", "ajax": false, "filename": "attendance.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.attendance"}, {"name": "1x layouts.administration.partials.menus.daily_break", "param_count": null, "params": [], "start": **********.157292, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/daily_break.blade.phplayouts.administration.partials.menus.daily_break", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fdaily_break.blade.php&line=1", "ajax": false, "filename": "daily_break.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.daily_break"}, {"name": "1x layouts.administration.partials.menus.leave", "param_count": null, "params": [], "start": **********.162435, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/leave.blade.phplayouts.administration.partials.menus.leave", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fleave.blade.php&line=1", "ajax": false, "filename": "leave.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.leave"}, {"name": "1x layouts.administration.partials.menus.penalty", "param_count": null, "params": [], "start": **********.172625, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/penalty.blade.phplayouts.administration.partials.menus.penalty", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fpenalty.blade.php&line=1", "ajax": false, "filename": "penalty.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.penalty"}, {"name": "1x layouts.administration.partials.menus.announcement", "param_count": null, "params": [], "start": **********.183412, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/announcement.blade.phplayouts.administration.partials.menus.announcement", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fannouncement.blade.php&line=1", "ajax": false, "filename": "announcement.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.announcement"}, {"name": "1x layouts.administration.partials.menus.task", "param_count": null, "params": [], "start": **********.191732, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/task.blade.phplayouts.administration.partials.menus.task", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Ftask.blade.php&line=1", "ajax": false, "filename": "task.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.task"}, {"name": "1x layouts.administration.partials.menus.daily_work_update", "param_count": null, "params": [], "start": **********.199786, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/daily_work_update.blade.phplayouts.administration.partials.menus.daily_work_update", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fdaily_work_update.blade.php&line=1", "ajax": false, "filename": "daily_work_update.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.daily_work_update"}, {"name": "1x layouts.administration.partials.menus.it_ticket", "param_count": null, "params": [], "start": **********.207142, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/it_ticket.blade.phplayouts.administration.partials.menus.it_ticket", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fit_ticket.blade.php&line=1", "ajax": false, "filename": "it_ticket.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.it_ticket"}, {"name": "1x layouts.administration.partials.menus.booking", "param_count": null, "params": [], "start": **********.21165, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/booking.blade.phplayouts.administration.partials.menus.booking", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fbooking.blade.php&line=1", "ajax": false, "filename": "booking.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.booking"}, {"name": "1x layouts.administration.partials.menus.quiz", "param_count": null, "params": [], "start": **********.221544, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/quiz.blade.phplayouts.administration.partials.menus.quiz", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fquiz.blade.php&line=1", "ajax": false, "filename": "quiz.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.quiz"}, {"name": "1x layouts.administration.partials.menus.salary", "param_count": null, "params": [], "start": **********.235877, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/salary.blade.phplayouts.administration.partials.menus.salary", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fsalary.blade.php&line=1", "ajax": false, "filename": "salary.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.salary"}, {"name": "1x layouts.administration.partials.menus.income_expense", "param_count": null, "params": [], "start": **********.238482, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/income_expense.blade.phplayouts.administration.partials.menus.income_expense", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fincome_expense.blade.php&line=1", "ajax": false, "filename": "income_expense.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.income_expense"}, {"name": "1x layouts.administration.partials.menus.logs", "param_count": null, "params": [], "start": **********.252228, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/logs.blade.phplayouts.administration.partials.menus.logs", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Flogs.blade.php&line=1", "ajax": false, "filename": "logs.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.logs"}, {"name": "1x layouts.administration.partials.menus.settings", "param_count": null, "params": [], "start": **********.254717, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/settings.blade.phplayouts.administration.partials.menus.settings", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fsettings.blade.php&line=1", "ajax": false, "filename": "settings.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.settings"}, {"name": "1x layouts.administration.partials.topnav", "param_count": null, "params": [], "start": **********.290042, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.phplayouts.administration.partials.topnav", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=1", "ajax": false, "filename": "topnav.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.topnav"}, {"name": "1x layouts.administration.partials.breadcrumb", "param_count": null, "params": [], "start": **********.324632, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/breadcrumb.blade.phplayouts.administration.partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.breadcrumb"}, {"name": "1x layouts.administration.partials.scripts", "param_count": null, "params": [], "start": **********.325563, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/scripts.blade.phplayouts.administration.partials.scripts", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.scripts"}, {"name": "1x sweetalert::alert", "param_count": null, "params": [], "start": **********.326518, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "sweetalert::alert"}]}, "route": {"uri": "GET task/show/{task}/{taskid}", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Task Read", "controller": "App\\Http\\Controllers\\Administration\\Task\\TaskController@show<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FTask%2FTaskController.php&line=107\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.task.show", "prefix": "/task", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FTask%2FTaskController.php&line=107\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Task/TaskController.php:107-141</a>"}, "queries": {"count": 143, "nb_statements": 143, "nb_visible_statements": 143, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.20528999999999992, "accumulated_duration_str": "205ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.0809438, "duration": 0.00567, "duration_str": "5.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 2.762}, {"sql": "select * from `tasks` where `id` = 17 and `tasks`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Traits/HasCustomRouteId.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Traits\\HasCustomRouteId.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 959}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php", "line": 214}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}], "start": **********.09856, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "HasCustomRouteId.php:19", "source": {"index": 17, "namespace": null, "name": "app/Traits/HasCustomRouteId.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Traits\\HasCustomRouteId.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FTraits%2FHasCustomRouteId.php&line=19", "ajax": false, "filename": "HasCustomRouteId.php", "line": "19"}, "connection": "blueorange", "explain": null, "start_percent": 2.762, "width_percent": 0.662}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1079009, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 3.424, "width_percent": 0.546}, {"sql": "select `value` from `settings` where `key` = 'mobile_restriction' limit 1", "type": "query", "params": [], "bindings": ["mobile_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.118593, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:31", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=31", "ajax": false, "filename": "RestrictDevices.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 3.97, "width_percent": 0.346}, {"sql": "select `value` from `settings` where `key` = 'computer_restriction' limit 1", "type": "query", "params": [], "bindings": ["computer_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.121061, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:32", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=32", "ajax": false, "filename": "RestrictDevices.php", "line": "32"}, "connection": "blueorange", "explain": null, "start_percent": 4.316, "width_percent": 0.321}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, {"index": 26, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 34}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 28, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1416771, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:36", "source": {"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=36", "ajax": false, "filename": "UserAccessors.php", "line": "36"}, "connection": "blueorange", "explain": null, "start_percent": 4.637, "width_percent": 0.731}, {"sql": "select `value` from `settings` where `key` = 'allowed_ip_ranges' limit 1", "type": "query", "params": [], "bindings": ["allowed_ip_ranges"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}], "start": **********.1489968, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "restrict.ip:25", "source": {"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictIpRange.php&line=25", "ajax": false, "filename": "RestrictIpRange.php", "line": "25"}, "connection": "blueorange", "explain": null, "start_percent": 5.368, "width_percent": 0.365}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.168925, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 5.733, "width_percent": 0.77}, {"sql": "select `users`.*, `task_user`.`task_id` as `pivot_task_id`, `task_user`.`user_id` as `pivot_user_id`, `task_user`.`progress` as `pivot_progress`, `task_user`.`created_at` as `pivot_created_at`, `task_user`.`updated_at` as `pivot_updated_at` from `users` inner join `task_user` on `users`.`id` = `task_user`.`user_id` where `task_user`.`task_id` = 17 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 111}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.18016, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "TaskController.php:111", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FTask%2FTaskController.php&line=111", "ajax": false, "filename": "TaskController.php", "line": "111"}, "connection": "blueorange", "explain": null, "start_percent": 6.503, "width_percent": 0.843}, {"sql": "select * from `tasks` where `id` = 17 and `taskid` = 'BOT20250717215526' and `tasks`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [17, "BOT20250717215526"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.184412, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 17, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 7.346, "width_percent": 0.36}, {"sql": "select * from `users` where `users`.`id` in (1) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.188606, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 7.706, "width_percent": 0.745}, {"sql": "select * from `employees` where `employees`.`user_id` in (1) and `employees`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.1973379, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 27, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 8.451, "width_percent": 0.706}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.202943, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 27, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 9.158, "width_percent": 0.497}, {"sql": "select * from `tasks` where `tasks`.`id` in (15) and `tasks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.207401, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 9.655, "width_percent": 0.37}, {"sql": "select * from `tasks` where `tasks`.`parent_task_id` in (17) and `tasks`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.211244, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 10.025, "width_percent": 0.395}, {"sql": "select `users`.*, `task_user`.`task_id` as `pivot_task_id`, `task_user`.`user_id` as `pivot_user_id`, `task_user`.`progress` as `pivot_progress`, `task_user`.`created_at` as `pivot_created_at`, `task_user`.`updated_at` as `pivot_updated_at` from `users` inner join `task_user` on `users`.`id` = `task_user`.`user_id` where `task_user`.`task_id` in (17) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2141669, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 21, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 10.419, "width_percent": 0.609}, {"sql": "select * from `employees` where `employees`.`user_id` in (1, 3, 23) and `employees`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.218613, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 26, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 11.028, "width_percent": 0.536}, {"sql": "select * from `media` where `media`.`model_id` in (1, 3, 23) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.223224, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 26, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 11.564, "width_percent": 0.794}, {"sql": "select * from `file_media` where `file_media`.`fileable_id` in (17) and `file_media`.`fileable_type` = 'App\\\\Models\\\\Task\\\\Task' and `file_media`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Task\\Task"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.228992, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 12.358, "width_percent": 0.828}, {"sql": "select * from `comments` where `comments`.`commentable_id` in (17) and `comments`.`commentable_type` = 'App\\\\Models\\\\Task\\\\Task' and `comments`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\Task\\Task"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 123}, {"index": 23, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.235556, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:123", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=123", "ajax": false, "filename": "TaskRepository.php", "line": "123"}, "connection": "blueorange", "explain": null, "start_percent": 13.186, "width_percent": 0.614}, {"sql": "select * from `comments` where `comments`.`commentable_id` in (17) and `comments`.`commentable_type` = 'App\\\\Models\\\\Task\\\\Task' and `comments`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\Task\\Task"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2384212, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 13.8, "width_percent": 0.39}, {"sql": "select * from `task_histories` where `task_histories`.`task_id` in (17) and `status` = 'Completed' and `task_histories`.`deleted_at` is null order by `ends_at` desc", "type": "query", "params": [], "bindings": ["Completed"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 126}, {"index": 23, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2441452, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:126", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=126", "ajax": false, "filename": "TaskRepository.php", "line": "126"}, "connection": "blueorange", "explain": null, "start_percent": 14.19, "width_percent": 0.648}, {"sql": "select * from `task_histories` where `task_histories`.`task_id` in (17) and `status` = 'Completed' and `task_histories`.`deleted_at` is null order by `ends_at` desc", "type": "query", "params": [], "bindings": ["Completed"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.247891, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=131", "ajax": false, "filename": "TaskRepository.php", "line": "131"}, "connection": "blueorange", "explain": null, "start_percent": 14.838, "width_percent": 0.502}, {"sql": "select exists(select * from `task_histories` where `task_histories`.`task_id` = 17 and `task_histories`.`task_id` is not null and `user_id` = 1 and `ends_at` is null and `status` = 'Working' and `task_histories`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": [17, 1, "Working"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 128}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.251688, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "TaskController.php:128", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FTask%2FTaskController.php&line=128", "ajax": false, "filename": "TaskController.php", "line": "128"}, "connection": "blueorange", "explain": null, "start_percent": 15.339, "width_percent": 0.614}, {"sql": "select * from `task_histories` where `task_histories`.`task_id` = 17 and `task_histories`.`task_id` is not null and `user_id` = 1 and `status` = 'Working' and `task_histories`.`deleted_at` is null order by `started_at` desc limit 1", "type": "query", "params": [], "bindings": [17, 1, "Working"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.256042, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "TaskController.php:135", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FTask%2FTaskController.php&line=135", "ajax": false, "filename": "TaskController.php", "line": "135"}, "connection": "blueorange", "explain": null, "start_percent": 15.953, "width_percent": 0.911}, {"sql": "select * from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 166}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 138}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.260767, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:166", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=166", "ajax": false, "filename": "TaskRepository.php", "line": "166"}, "connection": "blueorange", "explain": null, "start_percent": 16.864, "width_percent": 0.463}, {"sql": "(select `users`.* from `users` inner join `user_interactions` on `users`.`id` = `user_interactions`.`user_id` where `user_interactions`.`interacted_user_id` = 1 and `users`.`deleted_at` is null) union (select `users`.* from `users` inner join `user_interactions` on `users`.`id` = `user_interactions`.`interacted_user_id` where `user_interactions`.`user_id` = 1 and `users`.`deleted_at` is null)", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 147}, {"index": 19, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 163}, {"index": 24, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 166}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 138}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.268854, "duration": 0.01302, "duration_str": "13.02ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:147", "source": {"index": 13, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 147}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=147", "ajax": false, "filename": "UserAccessors.php", "line": "147"}, "connection": "blueorange", "explain": null, "start_percent": 17.327, "width_percent": 6.342}, {"sql": "select * from `users` where `id` in (1, 23, 5, 11, 2, 3, 4, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 81, 82, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 23, 5, 11, 2, 3, 4, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 81, 82, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 163}, {"index": 26, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 166}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 138}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.284231, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:148", "source": {"index": 15, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=148", "ajax": false, "filename": "UserAccessors.php", "line": "148"}, "connection": "blueorange", "explain": null, "start_percent": 23.669, "width_percent": 1.578}, {"sql": "select `users`.*, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `users` inner join `model_has_roles` on `users`.`id` = `model_has_roles`.`model_id` where `model_has_roles`.`role_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User' and not exists (select * from `tasks` inner join `task_user` on `tasks`.`id` = `task_user`.`task_id` where `users`.`id` = `task_user`.`user_id` and `task_id` = 17 and `tasks`.`deleted_at` is null) and `id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 81, 82, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126) and `status` = 'Active' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 17, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 81, 82, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, "Active"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 166}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Administration/Task/TaskController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Task\\TaskController.php", "line": 138}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.301852, "duration": 0.00966, "duration_str": "9.66ms", "memory": 0, "memory_str": null, "filename": "TaskRepository.php:166", "source": {"index": 19, "namespace": null, "name": "app/Repositories/Administration/Task/TaskRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Repositories\\Administration\\Task\\TaskRepository.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FRepositories%2FAdministration%2FTask%2FTaskRepository.php&line=166", "ajax": false, "filename": "TaskRepository.php", "line": "166"}, "connection": "blueorange", "explain": null, "start_percent": 25.247, "width_percent": 4.706}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.381356, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 29.953, "width_percent": 0.414}, {"sql": "select * from `users` where `users`.`id` = 3 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.386258, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 30.367, "width_percent": 0.453}, {"sql": "select * from `users` where `users`.`id` = 23 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.390744, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 30.82, "width_percent": 0.809}, {"sql": "select * from `employees` where `employees`.`user_id` = 2 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.671683, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 31.628, "width_percent": 0.755}, {"sql": "select * from `employees` where `employees`.`user_id` = 12 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.677133, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 32.383, "width_percent": 0.667}, {"sql": "select * from `employees` where `employees`.`user_id` = 17 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.682242, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 33.051, "width_percent": 0.672}, {"sql": "select * from `employees` where `employees`.`user_id` = 19 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.686227, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 33.723, "width_percent": 0.473}, {"sql": "select * from `employees` where `employees`.`user_id` = 21 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.689373, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 34.196, "width_percent": 0.409}, {"sql": "select * from `employees` where `employees`.`user_id` = 25 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.691796, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 34.605, "width_percent": 0.434}, {"sql": "select * from `employees` where `employees`.`user_id` = 26 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.696074, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 35.038, "width_percent": 0.531}, {"sql": "select * from `employees` where `employees`.`user_id` = 27 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.698842, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 35.569, "width_percent": 0.477}, {"sql": "select * from `employees` where `employees`.`user_id` = 28 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.701417, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 36.047, "width_percent": 0.482}, {"sql": "select * from `employees` where `employees`.`user_id` = 29 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.7040198, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 36.529, "width_percent": 0.55}, {"sql": "select * from `employees` where `employees`.`user_id` = 31 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.7066932, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 37.079, "width_percent": 0.487}, {"sql": "select * from `employees` where `employees`.`user_id` = 32 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.709486, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 37.566, "width_percent": 0.872}, {"sql": "select * from `employees` where `employees`.`user_id` = 33 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.714149, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 38.438, "width_percent": 0.555}, {"sql": "select * from `employees` where `employees`.`user_id` = 35 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.717141, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 38.994, "width_percent": 0.507}, {"sql": "select * from `employees` where `employees`.`user_id` = 36 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.720488, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 39.5, "width_percent": 0.443}, {"sql": "select * from `employees` where `employees`.`user_id` = 37 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.7231011, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 39.943, "width_percent": 0.487}, {"sql": "select * from `employees` where `employees`.`user_id` = 38 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.725723, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 40.431, "width_percent": 0.706}, {"sql": "select * from `employees` where `employees`.`user_id` = 40 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.7305331, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 41.137, "width_percent": 0.677}, {"sql": "select * from `employees` where `employees`.`user_id` = 41 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.734398, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 41.814, "width_percent": 0.434}, {"sql": "select * from `employees` where `employees`.`user_id` = 42 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.736912, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 42.248, "width_percent": 0.414}, {"sql": "select * from `employees` where `employees`.`user_id` = 43 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.739443, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 42.662, "width_percent": 0.434}, {"sql": "select * from `employees` where `employees`.`user_id` = 46 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.7427142, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 43.095, "width_percent": 0.838}, {"sql": "select * from `employees` where `employees`.`user_id` = 47 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.74731, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 43.933, "width_percent": 0.609}, {"sql": "select * from `employees` where `employees`.`user_id` = 50 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [50], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.750437, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 44.542, "width_percent": 0.473}, {"sql": "select * from `employees` where `employees`.`user_id` = 52 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.75299, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 45.014, "width_percent": 0.492}, {"sql": "select * from `employees` where `employees`.`user_id` = 53 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.756553, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 45.506, "width_percent": 0.468}, {"sql": "select * from `employees` where `employees`.`user_id` = 56 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [56], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.759475, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 45.974, "width_percent": 0.779}, {"sql": "select * from `employees` where `employees`.`user_id` = 57 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.764208, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 46.753, "width_percent": 0.687}, {"sql": "select * from `employees` where `employees`.`user_id` = 58 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.76723, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 47.44, "width_percent": 0.448}, {"sql": "select * from `employees` where `employees`.`user_id` = 59 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.7698052, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 47.888, "width_percent": 0.482}, {"sql": "select * from `employees` where `employees`.`user_id` = 60 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [60], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.7725918, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 48.371, "width_percent": 0.555}, {"sql": "select * from `employees` where `employees`.`user_id` = 61 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [61], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.7755208, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 48.926, "width_percent": 0.468}, {"sql": "select * from `employees` where `employees`.`user_id` = 62 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [62], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.779406, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 49.394, "width_percent": 0.677}, {"sql": "select * from `employees` where `employees`.`user_id` = 63 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.7827542, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 50.071, "width_percent": 0.487}, {"sql": "select * from `employees` where `employees`.`user_id` = 64 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [64], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.785614, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 50.558, "width_percent": 0.609}, {"sql": "select * from `employees` where `employees`.`user_id` = 65 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.789632, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 51.167, "width_percent": 0.658}, {"sql": "select * from `employees` where `employees`.`user_id` = 69 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [69], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.794347, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 51.824, "width_percent": 0.643}, {"sql": "select * from `employees` where `employees`.`user_id` = 71 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [71], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.798691, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 52.467, "width_percent": 0.594}, {"sql": "select * from `employees` where `employees`.`user_id` = 72 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [72], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.801462, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 53.062, "width_percent": 0.424}, {"sql": "select * from `employees` where `employees`.`user_id` = 73 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [73], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.804554, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 53.485, "width_percent": 0.453}, {"sql": "select * from `employees` where `employees`.`user_id` = 74 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.807097, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 53.938, "width_percent": 0.511}, {"sql": "select * from `employees` where `employees`.`user_id` = 78 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [78], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.810215, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 54.45, "width_percent": 0.911}, {"sql": "select * from `employees` where `employees`.`user_id` = 79 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [79], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.814829, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 55.361, "width_percent": 0.755}, {"sql": "select * from `employees` where `employees`.`user_id` = 81 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [81], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.818178, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 56.116, "width_percent": 0.443}, {"sql": "select * from `employees` where `employees`.`user_id` = 82 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [82], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.820755, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 56.559, "width_percent": 0.438}, {"sql": "select * from `employees` where `employees`.`user_id` = 84 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [84], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.823272, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 56.997, "width_percent": 0.487}, {"sql": "select * from `employees` where `employees`.`user_id` = 85 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [85], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.82612, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 57.485, "width_percent": 0.516}, {"sql": "select * from `employees` where `employees`.`user_id` = 86 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [86], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.83115, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 58.001, "width_percent": 0.536}, {"sql": "select * from `employees` where `employees`.`user_id` = 87 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.834712, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 58.537, "width_percent": 0.458}, {"sql": "select * from `employees` where `employees`.`user_id` = 88 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.8373082, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 58.995, "width_percent": 0.473}, {"sql": "select * from `employees` where `employees`.`user_id` = 89 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [89], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.839863, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 59.467, "width_percent": 0.492}, {"sql": "select * from `employees` where `employees`.`user_id` = 90 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [90], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.842588, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 59.959, "width_percent": 0.511}, {"sql": "select * from `employees` where `employees`.`user_id` = 91 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [91], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.847224, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 60.471, "width_percent": 0.706}, {"sql": "select * from `employees` where `employees`.`user_id` = 98 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [98], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.851182, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 61.177, "width_percent": 0.58}, {"sql": "select * from `employees` where `employees`.`user_id` = 99 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [99], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.8541331, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 61.757, "width_percent": 0.497}, {"sql": "select * from `employees` where `employees`.`user_id` = 100 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [100], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.856662, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 62.253, "width_percent": 0.492}, {"sql": "select * from `employees` where `employees`.`user_id` = 101 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [101], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.859339, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 62.745, "width_percent": 0.516}, {"sql": "select * from `employees` where `employees`.`user_id` = 102 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [102], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.864912, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 63.262, "width_percent": 1.033}, {"sql": "select * from `employees` where `employees`.`user_id` = 103 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [103], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.869112, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 64.294, "width_percent": 0.74}, {"sql": "select * from `employees` where `employees`.`user_id` = 104 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [104], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.873298, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 65.035, "width_percent": 0.638}, {"sql": "select * from `employees` where `employees`.`user_id` = 105 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [105], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.8789291, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 65.673, "width_percent": 1.681}, {"sql": "select * from `employees` where `employees`.`user_id` = 106 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.885355, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 67.353, "width_percent": 0.604}, {"sql": "select * from `employees` where `employees`.`user_id` = 107 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [107], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.88905, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 67.958, "width_percent": 0.482}, {"sql": "select * from `employees` where `employees`.`user_id` = 108 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [108], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.8916519, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 68.44, "width_percent": 0.438}, {"sql": "select * from `employees` where `employees`.`user_id` = 109 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [109], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.894463, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 68.878, "width_percent": 1.559}, {"sql": "select * from `employees` where `employees`.`user_id` = 110 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [110], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9005158, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 70.437, "width_percent": 0.546}, {"sql": "select * from `employees` where `employees`.`user_id` = 112 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [112], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.903272, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 70.983, "width_percent": 0.438}, {"sql": "select * from `employees` where `employees`.`user_id` = 113 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [113], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.905855, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 71.421, "width_percent": 0.438}, {"sql": "select * from `employees` where `employees`.`user_id` = 114 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [114], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9083302, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 71.859, "width_percent": 0.477}, {"sql": "select * from `employees` where `employees`.`user_id` = 115 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [115], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.911572, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 72.337, "width_percent": 0.95}, {"sql": "select * from `employees` where `employees`.`user_id` = 116 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [116], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9162982, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 73.287, "width_percent": 0.643}, {"sql": "select * from `employees` where `employees`.`user_id` = 117 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [117], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.919345, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 73.93, "width_percent": 0.458}, {"sql": "select * from `employees` where `employees`.`user_id` = 118 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [118], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.921896, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 74.387, "width_percent": 0.443}, {"sql": "select * from `employees` where `employees`.`user_id` = 119 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [119], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.924323, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 74.831, "width_percent": 0.482}, {"sql": "select * from `employees` where `employees`.`user_id` = 120 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [120], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.92734, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 75.313, "width_percent": 1.159}, {"sql": "select * from `employees` where `employees`.`user_id` = 123 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [123], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.932491, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 76.472, "width_percent": 0.492}, {"sql": "select * from `employees` where `employees`.`user_id` = 124 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [124], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9350731, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 76.964, "width_percent": 0.414}, {"sql": "select * from `employees` where `employees`.`user_id` = 125 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [125], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.937487, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 77.378, "width_percent": 0.453}, {"sql": "select * from `employees` where `employees`.`user_id` = 126 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [126], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9400501, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 77.831, "width_percent": 0.409}, {"sql": "select * from `employees` where `employees`.`user_id` = 13 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.942732, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 78.241, "width_percent": 0.516}, {"sql": "select * from `employees` where `employees`.`user_id` = 22 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.94755, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 78.757, "width_percent": 0.604}, {"sql": "select * from `employees` where `employees`.`user_id` = 45 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.951606, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 79.361, "width_percent": 0.687}, {"sql": "select * from `employees` where `employees`.`user_id` = 10 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9548762, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 80.048, "width_percent": 0.482}, {"sql": "select * from `employees` where `employees`.`user_id` = 14 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.957895, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 80.53, "width_percent": 0.594}, {"sql": "select * from `employees` where `employees`.`user_id` = 18 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.961663, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 81.124, "width_percent": 1.184}, {"sql": "select * from `employees` where `employees`.`user_id` = 11 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9667752, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 82.308, "width_percent": 0.57}, {"sql": "select * from `employees` where `employees`.`user_id` = 5 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.970754, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 82.878, "width_percent": 0.697}, {"sql": "select * from `employees` where `employees`.`user_id` = 4 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.97492, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 83.574, "width_percent": 0.521}, {"sql": "select * from `employees` where `employees`.`user_id` = 6 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.978195, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 84.096, "width_percent": 1.32}, {"sql": "select * from `employees` where `employees`.`user_id` = 7 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.983799, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 85.416, "width_percent": 0.589}, {"sql": "select * from `employees` where `employees`.`user_id` = 70 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [70], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.987612, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 86.005, "width_percent": 0.585}, {"sql": "select * from `employees` where `employees`.`user_id` = 76 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [76], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.991278, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 86.59, "width_percent": 0.589}, {"sql": "select * from `employees` where `employees`.`user_id` = 77 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [77], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9951298, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 87.179, "width_percent": 0.887}, {"sql": "select * from `employees` where `employees`.`user_id` = 8 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9999049, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 88.066, "width_percent": 0.619}, {"sql": "select * from `employees` where `employees`.`user_id` = 15 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.003115, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 88.684, "width_percent": 0.58}, {"sql": "select * from `employees` where `employees`.`user_id` = 20 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.006073, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 89.264, "width_percent": 0.448}, {"sql": "select * from `employees` where `employees`.`user_id` = 9 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.0092661, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 89.712, "width_percent": 0.473}, {"sql": "select * from `employees` where `employees`.`user_id` = 16 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.01374, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 90.185, "width_percent": 0.653}, {"sql": "select * from `employees` where `employees`.`user_id` = 93 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [93], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.01766, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 90.837, "width_percent": 0.619}, {"sql": "select * from `employees` where `employees`.`user_id` = 95 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [95], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.0212219, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 91.456, "width_percent": 0.541}, {"sql": "select * from `employees` where `employees`.`user_id` = 96 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.0248442, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 91.997, "width_percent": 0.482}, {"sql": "select * from `employees` where `employees`.`user_id` = 97 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [97], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.028045, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 92.479, "width_percent": 1.023}, {"sql": "select * from `employees` where `employees`.`user_id` = 122 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [122], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.032661, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 93.502, "width_percent": 0.701}, {"sql": "select * from `employees` where `employees`.`user_id` = 121 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [121], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.036862, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 94.203, "width_percent": 0.536}, {"sql": "select * from `employees` where `employees`.`user_id` = 111 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [111], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.task.modals.add_assignees", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/task/modals/add_assignees.blade.php", "line": 29}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.0404148, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 94.739, "width_percent": 0.546}, {"sql": "select count(*) as aggregate from `chattings` where `receiver_id` = 1 and `seen_at` is null and `chattings`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/ChattingHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\ChattingHelper.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.136905, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "ChattingHelper.php:61", "source": {"index": 16, "namespace": null, "name": "app/Helpers/ChattingHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\ChattingHelper.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FChattingHelper.php&line=61", "ajax": false, "filename": "ChattingHelper.php", "line": "61"}, "connection": "blueorange", "explain": null, "start_percent": 95.285, "width_percent": 0.745}, {"sql": "select * from `shortcuts` where `shortcuts`.`user_id` = 1 and `shortcuts`.`user_id` is not null and `shortcuts`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 67}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.293663, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "layouts.administration.partials.topnav:67", "source": {"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=67", "ajax": false, "filename": "topnav.blade.php", "line": "67"}, "connection": "blueorange", "explain": null, "start_percent": 96.03, "width_percent": 0.653}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.2987988, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "layouts.administration.partials.topnav:88", "source": {"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=88", "ajax": false, "filename": "topnav.blade.php", "line": "88"}, "connection": "blueorange", "explain": null, "start_percent": 96.683, "width_percent": 1.232}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 158}], "start": **********.305305, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 97.915, "width_percent": 0.765}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.312037, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 98.68, "width_percent": 0.585}, {"sql": "select * from `employees` where `employees`.`user_id` = 1 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 185}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.319838, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "layouts.administration.partials.topnav:185", "source": {"index": 21, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=185", "ajax": false, "filename": "topnav.blade.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 99.264, "width_percent": 0.736}]}, "models": {"data": {"App\\Models\\User": {"value": 241, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\User\\Employee\\Employee": {"value": 110, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FEmployee%2FEmployee.php&line=1", "ajax": false, "filename": "Employee.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}, "App\\Models\\Settings\\Settings": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "App\\Models\\Task\\Task": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FTask%2FTask.php&line=1", "ajax": false, "filename": "Task.php", "line": "?"}}, "App\\Models\\FileMedia\\FileMedia": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FFileMedia%2FFileMedia.php&line=1", "ajax": false, "filename": "FileMedia.php", "line": "?"}}}, "count": 387, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 79, "messages": [{"message": "[\n  ability => Task Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-764607249 data-indent-pad=\"  \"><span class=sf-dump-note>Task Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Task Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764607249\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.177702, "xdebug_link": null}, {"message": "[\n  ability => Task Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1911823074 data-indent-pad=\"  \"><span class=sf-dump-note>Task Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Task Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1911823074\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.339342, "xdebug_link": null}, {"message": "[\n  ability => Task Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-576641401 data-indent-pad=\"  \"><span class=sf-dump-note>Task Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Task Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-576641401\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.395974, "xdebug_link": null}, {"message": "[\n  ability => Task Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1572963035 data-indent-pad=\"  \"><span class=sf-dump-note>Task Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Task Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1572963035\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.39725, "xdebug_link": null}, {"message": "[\n  ability => Vault Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-373627474 data-indent-pad=\"  \"><span class=sf-dump-note>Vault Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Vault Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373627474\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.141773, "xdebug_link": null}, {"message": "[\n  ability => Vault Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1313498065 data-indent-pad=\"  \"><span class=sf-dump-note>Vault Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Vault Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313498065\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.142727, "xdebug_link": null}, {"message": "[\n  ability => Vault Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-949631728 data-indent-pad=\"  \"><span class=sf-dump-note>Vault Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Vault Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-949631728\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.144015, "xdebug_link": null}, {"message": "[\n  ability => Attendance Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1362222499 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362222499\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.147842, "xdebug_link": null}, {"message": "[\n  ability => Attendance Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-612780988 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612780988\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.149557, "xdebug_link": null}, {"message": "[\n  ability => Attendance Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-107742513 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Attendance Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107742513\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.151329, "xdebug_link": null}, {"message": "[\n  ability => Attendance Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1536487519 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Attendance Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1536487519\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.152669, "xdebug_link": null}, {"message": "[\n  ability => Attendance Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2071520743 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071520743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.153651, "xdebug_link": null}, {"message": "[\n  ability => Attendance Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-417014057 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Attendance Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-417014057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.154817, "xdebug_link": null}, {"message": "[\n  ability => Attendance Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-632956477 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-632956477\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.155652, "xdebug_link": null}, {"message": "[\n  ability => Attendance Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-357787157 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Attendance Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-357787157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.156647, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1964002264 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Daily Break Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1964002264\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.158694, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-225343372 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Daily Break Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-225343372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.159877, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1763767184 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Daily Break Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763767184\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.160828, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1403662183 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Daily Break Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1403662183\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.161793, "xdebug_link": null}, {"message": "[\n  ability => Leave History Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-446392003 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Leave History Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446392003\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.165721, "xdebug_link": null}, {"message": "[\n  ability => Leave History Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-873972404 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Leave History Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873972404\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.168023, "xdebug_link": null}, {"message": "[\n  ability => Leave History Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1439092130 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Leave History Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439092130\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.170466, "xdebug_link": null}, {"message": "[\n  ability => Leave History Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1026083900 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Leave History Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026083900\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.172102, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1374273363 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374273363\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.174307, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-392300648 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-392300648\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.176728, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-18564923 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18564923\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.179027, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1857255347 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857255347\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.182246, "xdebug_link": null}, {"message": "[\n  ability => Announcement Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1494152439 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Announcement Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1494152439\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.185453, "xdebug_link": null}, {"message": "[\n  ability => Announcement Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-237059518 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Announcement Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237059518\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.187404, "xdebug_link": null}, {"message": "[\n  ability => Announcement Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1119875855 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Announcement Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119875855\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.189263, "xdebug_link": null}, {"message": "[\n  ability => Announcement Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-669458415 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Announcement Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-669458415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.19091, "xdebug_link": null}, {"message": "[\n  ability => Task Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-383423646 data-indent-pad=\"  \"><span class=sf-dump-note>Task Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Task Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-383423646\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.193717, "xdebug_link": null}, {"message": "[\n  ability => Task Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-198760687 data-indent-pad=\"  \"><span class=sf-dump-note>Task Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Task Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198760687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.195024, "xdebug_link": null}, {"message": "[\n  ability => Task Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1683881042 data-indent-pad=\"  \"><span class=sf-dump-note>Task Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Task Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1683881042\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.197094, "xdebug_link": null}, {"message": "[\n  ability => Task Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1611689450 data-indent-pad=\"  \"><span class=sf-dump-note>Task Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Task Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611689450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.19898, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1242675321 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1242675321\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.201703, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-525423476 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-525423476\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.20285, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1483142111 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483142111\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.203704, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Delete,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1825437136 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825437136\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.204502, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-163279707 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Daily Work Update Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-163279707\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.205595, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1637078643 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637078643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.20667, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-586495094 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">IT Ticket Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-586495094\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.208347, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1369316401 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">IT Ticket Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369316401\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.209181, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1723192029 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">IT Ticket Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723192029\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.210172, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1830454655 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">IT Ticket Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1830454655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.211181, "xdebug_link": null}, {"message": "[\n  ability => Dining Room Booking Everything,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-745276351 data-indent-pad=\"  \"><span class=sf-dump-note>Dining Room Booking Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Dining Room Booking Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745276351\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.214904, "xdebug_link": null}, {"message": "[\n  ability => Dining Room Booking Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1693328129 data-indent-pad=\"  \"><span class=sf-dump-note>Dining Room Booking Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Dining Room Booking Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693328129\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.218281, "xdebug_link": null}, {"message": "[\n  ability => Dining Room Booking Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-225685943 data-indent-pad=\"  \"><span class=sf-dump-note>Dining Room Booking Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Dining Room Booking Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-225685943\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.220829, "xdebug_link": null}, {"message": "[\n  ability => Quiz Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-144977357 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Quiz Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-144977357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.224577, "xdebug_link": null}, {"message": "[\n  ability => Quiz Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1873302108 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Quiz Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1873302108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.225914, "xdebug_link": null}, {"message": "[\n  ability => Quiz Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1040616348 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Quiz Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1040616348\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.22729, "xdebug_link": null}, {"message": "[\n  ability => Quiz Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-923828130 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Quiz Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-923828130\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.228772, "xdebug_link": null}, {"message": "[\n  ability => Quiz Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1229104961 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Quiz Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229104961\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.230898, "xdebug_link": null}, {"message": "[\n  ability => Quiz Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1229516356 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Quiz Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229516356\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.234957, "xdebug_link": null}, {"message": "[\n  ability => Salary Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-996137789 data-indent-pad=\"  \"><span class=sf-dump-note>Salary Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Salary Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-996137789\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.237381, "xdebug_link": null}, {"message": "[\n  ability => Income Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-164645665 data-indent-pad=\"  \"><span class=sf-dump-note>Income Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Income Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-164645665\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.240229, "xdebug_link": null}, {"message": "[\n  ability => Income Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-998561803 data-indent-pad=\"  \"><span class=sf-dump-note>Income Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Income Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998561803\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.241879, "xdebug_link": null}, {"message": "[\n  ability => Income Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-219670062 data-indent-pad=\"  \"><span class=sf-dump-note>Income Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Income Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219670062\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.243077, "xdebug_link": null}, {"message": "[\n  ability => Income Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-808659855 data-indent-pad=\"  \"><span class=sf-dump-note>Income Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Income Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808659855\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.24433, "xdebug_link": null}, {"message": "[\n  ability => Expense Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1821204679 data-indent-pad=\"  \"><span class=sf-dump-note>Expense Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Expense Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821204679\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.245588, "xdebug_link": null}, {"message": "[\n  ability => Expense Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1591252546 data-indent-pad=\"  \"><span class=sf-dump-note>Expense Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Expense Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591252546\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.248111, "xdebug_link": null}, {"message": "[\n  ability => Expense Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-843964658 data-indent-pad=\"  \"><span class=sf-dump-note>Expense Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Expense Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-843964658\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.251333, "xdebug_link": null}, {"message": "[\n  ability => Logs Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-118851079 data-indent-pad=\"  \"><span class=sf-dump-note>Logs Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Logs Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-118851079\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.253988, "xdebug_link": null}, {"message": "[\n  ability => App Setting Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1893692115 data-indent-pad=\"  \"><span class=sf-dump-note>App Setting Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App Setting Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893692115\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.256403, "xdebug_link": null}, {"message": "[\n  ability => App Setting Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1382584515 data-indent-pad=\"  \"><span class=sf-dump-note>App Setting Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App Setting Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1382584515\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.257213, "xdebug_link": null}, {"message": "[\n  ability => App Setting Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-844322554 data-indent-pad=\"  \"><span class=sf-dump-note>App Setting Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App Setting Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-844322554\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.258005, "xdebug_link": null}, {"message": "[\n  ability => Weekend Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1182677631 data-indent-pad=\"  \"><span class=sf-dump-note>Weekend Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Weekend Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182677631\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.259245, "xdebug_link": null}, {"message": "[\n  ability => Holiday Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-996330644 data-indent-pad=\"  \"><span class=sf-dump-note>Holiday Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Holiday Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-996330644\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.260631, "xdebug_link": null}, {"message": "[\n  ability => User Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1165641954 data-indent-pad=\"  \"><span class=sf-dump-note>User Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">User Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165641954\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.261813, "xdebug_link": null}, {"message": "[\n  ability => User Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1541475285 data-indent-pad=\"  \"><span class=sf-dump-note>User Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">User Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541475285\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.262992, "xdebug_link": null}, {"message": "[\n  ability => User Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1781128421 data-indent-pad=\"  \"><span class=sf-dump-note>User Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">User Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781128421\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.265408, "xdebug_link": null}, {"message": "[\n  ability => User Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1319303878 data-indent-pad=\"  \"><span class=sf-dump-note>User Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">User Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319303878\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.268982, "xdebug_link": null}, {"message": "[\n  ability => Permission Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-652206007 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Permission Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-652206007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.270941, "xdebug_link": null}, {"message": "[\n  ability => Role Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-824234588 data-indent-pad=\"  \"><span class=sf-dump-note>Role Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Role Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-824234588\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.272153, "xdebug_link": null}, {"message": "[\n  ability => Role Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1824876936 data-indent-pad=\"  \"><span class=sf-dump-note>Role Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Role Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824876936\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.274232, "xdebug_link": null}, {"message": "[\n  ability => Role Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-591146655 data-indent-pad=\"  \"><span class=sf-dump-note>Role Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Role Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591146655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.276203, "xdebug_link": null}, {"message": "[\n  ability => Permission Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-549690588 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Permission Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549690588\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.278531, "xdebug_link": null}, {"message": "[\n  ability => Permission Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1849580303 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Permission Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1849580303\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.28501, "xdebug_link": null}, {"message": "[\n  ability => Permission Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1226022050 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Permission Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226022050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.288449, "xdebug_link": null}]}, "session": {"_token": "FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/file/file/download/vOP60wQVep5G1yJq\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1752832853\n]", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/task/show/ny9qVRQEBxdzBJK6/BOT20250717215526", "action_name": "administration.task.show", "controller_action": "App\\Http\\Controllers\\Administration\\Task\\TaskController@show", "uri": "GET task/show/{task}/{taskid}", "controller": "App\\Http\\Controllers\\Administration\\Task\\TaskController@show<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FTask%2FTaskController.php&line=107\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/task", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FTask%2FTaskController.php&line=107\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Task/TaskController.php:107-141</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Task Read", "duration": "2.27s", "peak_memory": "36MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-204560409 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-204560409\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-791081297 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-791081297\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2132522360 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://blueorange.test/task/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1072 characters\">laravel_session=eyJpdiI6ImNvQmlPQVRUMFRyZ3FTd21MRm9ib0E9PSIsInZhbHVlIjoiRGpIWCs4QW44Qkgyc2JuL25qRUdCRVgvaTN5cWx0U3YramV2ZU5kdXVRUCtmTUNkazFGZFdDNzZERXZjRExnYVJ6VlgveWRoUDB0YWh1cTVsTHpJenV1NTBwajNDYWVrV09MVkhHdEVjck1UWmk0NEhVZHFJbG5RN1prTFY2SEkiLCJtYWMiOiIzYzE4ZTgxMzZhZjNlMTA0YmY1MjEwYTljZDNmNGU4ZjYyOWE0YjFkNWJhOWFjNWMwOWU3ZTI4MTc4OWE3MDhlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikx0a01OTkc2WkFOMDJjNlVPSTVPV3c9PSIsInZhbHVlIjoidDVLU0NqSHdzQVJ1MDVIL3RVRGtzcG1tTXY4L2ZVeHRoSTdEZmRzOVMwWDlzTHFBYS8rS29VRlhINllFMjRDWEs0Yk5PZlBldUNCV0VML00ycFE2MW9SRzBYbW1za1Jiam9rRE82eVhmM09iSy9oY0Rub3ZxbGw5VWZLbENRRHkiLCJtYWMiOiIyMWQ0MmIxNjMwMTY5Y2JiMDhkZDI2MTVkMDNkYmViMDc2OTlkZmRjODZiZTBiMGQyMTkyNmY0N2U0ZWQyMDFlIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6IlBhRG1CMFlRY0VDTlFRS3M4RlVsdnc9PSIsInZhbHVlIjoiY0REeWROeG41ckVsQW13ZjFDRkZCZmdFd25VMkp0MVRDR0puVEUzZFlvLyt3VTUzOEliUnVQWVlTLzZCUUFSQm5VbjRYd3lWQ3FMcko2RE5QajlrOXYva1Y4UU5sOEJ2RXg2ZVhVb1RQcVJaSW5INnloYS9UYk5DK1hIUW5heTkiLCJtYWMiOiJiMGU5MzMwYTNhYjFiZmI2M2ZjYzI5YjZkODJiOTViNWQ3M2FmMjVmZWEzMWI2OGNlYzIyNDNjZTg0MGQ5YjQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132522360\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1257545571 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iKklXT5rhoiaJF6thcK0rADvZqtOSIW37uJdoTiC</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pU9hKUo8erm4hsoGsDajoymGto9RQabaIruS2lk5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1257545571\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1771401522 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 10:31:28 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771401522\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1207109729 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://blueorange.test/file/file/download/vOP60wQVep5G1yJq</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752832853</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207109729\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/task/show/ny9qVRQEBxdzBJK6/BOT20250717215526", "action_name": "administration.task.show", "controller_action": "App\\Http\\Controllers\\Administration\\Task\\TaskController@show"}, "badge": null}}